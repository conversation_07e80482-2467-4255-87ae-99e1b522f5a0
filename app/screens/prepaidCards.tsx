import React, { useState, useEffect } from 'react';
import { View, Text, TouchableOpacity, TextInput, StyleSheet, ScrollView, Alert, Modal, Platform } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import i18n from '../i18n';
import { useSettings } from '../context/SettingsContext';
import { getPrepaidCards, addPrepaidCard, updatePrepaidCard, deletePrepaidCard, addPrepaidCardUsage, getPrepaidCardUsage, updatePrepaidCardUsage, deletePrepaidCardUsage } from '../constants/Storage';
import DateTimePicker from '@react-native-community/datetimepicker';

interface PrepaidCard {
    id: number;
    name: string;
    card_type: 'amount' | 'count';
    balance: number;
    count_balance: number;
    recharge_date: string;
    expiry_date?: string;
    created_at: string;
}

interface PrepaidCardUsage {
    id: number;
    card_id: number;
    amount: number;
    note: string;
    usage_date: string;
    created_at: string;
}

const PrepaidCards = () => {
    const [cards, setCards] = useState<PrepaidCard[]>([]);
    const [showAddModal, setShowAddModal] = useState(false);
    const [editingCard, setEditingCard] = useState<PrepaidCard | null>(null);
    const [cardName, setCardName] = useState('');
    const [cardType, setCardType] = useState<'amount' | 'count'>('amount');
    const [cardBalance, setCardBalance] = useState('');
    const [cardCountBalance, setCardCountBalance] = useState('');
    const [rechargeDate, setRechargeDate] = useState(new Date());
    const [expiryDate, setExpiryDate] = useState<Date | null>(null);
    const [showDatePicker, setShowDatePicker] = useState(false);
    const [showExpiryDatePicker, setShowExpiryDatePicker] = useState(false);

    // Usage modal states
    const [showUsageModal, setShowUsageModal] = useState(false);
    const [selectedCard, setSelectedCard] = useState<PrepaidCard | null>(null);
    const [usageAmount, setUsageAmount] = useState('');
    const [usageNote, setUsageNote] = useState('');
    const [usageDate, setUsageDate] = useState(new Date());
    const [showUsageDatePicker, setShowUsageDatePicker] = useState(false);

    // Detail modal states
    const [showDetailModal, setShowDetailModal] = useState(false);
    const [usageRecords, setUsageRecords] = useState<PrepaidCardUsage[]>([]);
    const [editingUsage, setEditingUsage] = useState<PrepaidCardUsage | null>(null);

    const { currency } = useSettings();

    useEffect(() => {
        loadCards();
    }, []);

    const loadCards = async () => {
        try {
            const cardsData = await getPrepaidCards();
            setCards(cardsData);
        } catch (error) {
            console.error('Failed to load prepaid cards:', error);
        }
    };

    const handleAddCard = async () => {
        if (!cardName.trim()) {
            Alert.alert(i18n.t('common.error'), i18n.t('prepaidCards.cardNameRequired'));
            return;
        }

        // 验证金额或次数
        if (cardType === 'amount') {
            const balance = parseFloat(cardBalance);
            if (!cardBalance.trim() || isNaN(balance) || balance < 0) {
                Alert.alert(i18n.t('common.error'), i18n.t('prepaidCards.invalidBalance'));
                return;
            }
        } else {
            const countBalance = parseInt(cardCountBalance);
            if (!cardCountBalance.trim() || isNaN(countBalance) || countBalance < 0) {
                Alert.alert(i18n.t('common.error'), i18n.t('prepaidCards.invalidCountBalance'));
                return;
            }
        }

        try {
            if (editingCard) {
                // 更新现有卡片
                await updatePrepaidCard(editingCard.id, {
                    name: cardName.trim(),
                    card_type: cardType,
                    balance: cardType === 'amount' ? parseFloat(cardBalance) : 0,
                    count_balance: cardType === 'count' ? parseInt(cardCountBalance) : 0,
                    recharge_date: rechargeDate.toISOString().split('T')[0],
                    expiry_date: expiryDate ? expiryDate.toISOString().split('T')[0] : undefined,
                });
            } else {
                // 添加新卡片
                await addPrepaidCard({
                    name: cardName.trim(),
                    card_type: cardType,
                    balance: cardType === 'amount' ? parseFloat(cardBalance) : 0,
                    count_balance: cardType === 'count' ? parseInt(cardCountBalance) : 0,
                    recharge_date: rechargeDate.toISOString().split('T')[0],
                    expiry_date: expiryDate ? expiryDate.toISOString().split('T')[0] : undefined,
                });
            }

            resetForm();
            setShowAddModal(false);
            loadCards(); // 重新加载数据
        } catch (error) {
            console.error('Failed to save card:', error);
            Alert.alert(i18n.t('common.error'), i18n.t('prepaidCards.addCardFailed'));
        }
    };

    const handleEditCard = (card: PrepaidCard) => {
        setEditingCard(card);
        setCardName(card.name);
        setCardType(card.card_type || 'amount');
        setCardBalance(card.balance.toString());
        setCardCountBalance(card.count_balance?.toString() || '0');
        setRechargeDate(new Date(card.recharge_date));
        setExpiryDate(card.expiry_date ? new Date(card.expiry_date) : null);
        setShowAddModal(true);
    };

    const handleDeleteCard = async (cardId: number) => {

        Alert.alert(
            i18n.t('prepaidCards.confirmDelete'),
            i18n.t('prepaidCards.confirmDeleteMessage'),
            [
                { text: i18n.t('common.cancel'), style: 'cancel' },
                {
                    text: i18n.t('common.delete'),
                    style: 'destructive',
                    onPress: async () => {
                        try {
                            await deletePrepaidCard(cardId);
                            loadCards(); // 重新加载数据
                        } catch (error) {
                            console.error('Failed to delete card:', error);
                            Alert.alert(i18n.t('common.error'), i18n.t('prepaidCards.deleteCardFailed'));
                        }
                    }
                }
            ]
        );
    };

    const resetForm = () => {
        setCardName('');
        setCardType('amount');
        setCardBalance('');
        setCardCountBalance('');
        setRechargeDate(new Date());
        setExpiryDate(null);
        setEditingCard(null);
    };

    const resetUsageForm = () => {
        setUsageAmount('');
        setUsageNote('');
        setUsageDate(new Date());
        setSelectedCard(null);
        setEditingUsage(null);
    };

    const handleCardClick = (card: PrepaidCard) => {
        setSelectedCard(card);
        setShowUsageModal(true);
    };

    const handleUseCard = async () => {
        if (!selectedCard) return;

        const amount = parseFloat(usageAmount);
        if (isNaN(amount) || amount <= 0) {
            Alert.alert(i18n.t('common.error'), i18n.t('prepaidCards.invalidUsageAmount'));
            return;
        }

        // 根据卡片类型检查余额
        if (selectedCard.card_type === 'count') {
            // 次卡：检查次数余额
            if (amount > selectedCard.count_balance) {
                Alert.alert(i18n.t('common.error'), i18n.t('prepaidCards.insufficientCount'));
                return;
            }
        } else {
            // 金额卡：检查金额余额
            if (amount > selectedCard.balance) {
                Alert.alert(i18n.t('common.error'), i18n.t('prepaidCards.insufficientBalance'));
                return;
            }
        }

        try {
            if (editingUsage) {
                // 编辑现有使用记录
                await updatePrepaidCardUsage(editingUsage.id, {
                    amount: amount,
                    note: usageNote.trim(),
                    usage_date: usageDate.toISOString().split('T')[0],
                });
                // 重新加载使用记录
                if (selectedCard) {
                    const records = await getPrepaidCardUsage(selectedCard.id);
                    setUsageRecords(records);
                }
            } else {
                // 添加新的使用记录
                await addPrepaidCardUsage({
                    card_id: selectedCard.id,
                    amount: amount,
                    note: usageNote.trim(),
                    usage_date: usageDate.toISOString().split('T')[0],
                });
            }

            resetUsageForm();
            setShowUsageModal(false);
            setEditingUsage(null);
            loadCards(); // 重新加载数据
        } catch (error) {
            console.error('Failed to use card:', error);
            Alert.alert(i18n.t('common.error'), i18n.t('prepaidCards.useCardFailed'));
        }
    };

    const handleViewDetails = async (card: PrepaidCard) => {
        try {
            const records = await getPrepaidCardUsage(card.id);
            setUsageRecords(records);
            setSelectedCard(card);
            setShowDetailModal(true);
        } catch (error) {
            console.error('Failed to load usage records:', error);
            Alert.alert(i18n.t('common.error'), i18n.t('prepaidCards.loadRecordsFailed'));
        }
    };

    const handleEditUsage = (usage: PrepaidCardUsage) => {
        setEditingUsage(usage);
        setUsageAmount(usage.amount.toString());
        setUsageNote(usage.note || '');
        setUsageDate(new Date(usage.usage_date));
        setShowUsageModal(true);
    };

    const handleDeleteUsage = (usageId: number) => {
        Alert.alert(
            i18n.t('common.confirm'),
            i18n.t('prepaidCards.confirmDeleteUsage'),
            [
                { text: i18n.t('common.cancel'), style: 'cancel' },
                {
                    text: i18n.t('common.delete'),
                    style: 'destructive',
                    onPress: async () => {
                        try {
                            await deletePrepaidCardUsage(usageId);
                            // 重新加载使用记录
                            if (selectedCard) {
                                const records = await getPrepaidCardUsage(selectedCard.id);
                                setUsageRecords(records);
                            }
                            loadCards(); // 重新加载卡片数据以更新余额
                        } catch (error) {
                            console.error('Failed to delete usage record:', error);
                            Alert.alert(i18n.t('common.error'), i18n.t('prepaidCards.deleteUsageFailed'));
                        }
                    }
                }
            ]
        );
    };

    const renderCard = (card: PrepaidCard) => (
        <TouchableOpacity
            key={card.id}
            style={styles.cardItem}
            onPress={() => handleCardClick(card)}
            activeOpacity={0.7}
        >
            <View style={styles.cardHeader}>
                <View style={styles.cardIcon}>
                    <Ionicons name="card" size={24} color="#E91E63" />
                </View>
                <View style={styles.cardInfo}>
                    <Text style={styles.cardName}>{card.name}</Text>
                    <Text style={styles.cardBalance}>
                        {card.card_type === 'amount'
                            ? `${i18n.t('prepaidCards.balance')}: ${currency}${card.balance.toFixed(2)}`
                            : `${i18n.t('prepaidCards.remainingCount')}: ${card.count_balance}${i18n.t('prepaidCards.times')}`
                        }
                    </Text>
                    <Text style={styles.cardDate}>
                        {i18n.t('prepaidCards.rechargeDate')}: {card.recharge_date}
                    </Text>
                    {card.expiry_date && (
                        <Text style={styles.cardDate}>
                            {i18n.t('prepaidCards.expiryDate')}: {card.expiry_date}
                        </Text>
                    )}
                </View>
                <View style={styles.cardActions}>
                    <TouchableOpacity
                        style={styles.actionButton}
                        onPress={(e) => {
                            e.stopPropagation();
                            handleViewDetails(card);
                        }}
                    >
                        <Ionicons name="list-outline" size={20} color="#4CAF50" />
                    </TouchableOpacity>
                    <TouchableOpacity
                        style={styles.actionButton}
                        onPress={(e) => {
                            e.stopPropagation();
                            handleEditCard(card);
                        }}
                    >
                        <Ionicons name="pencil-outline" size={20} color="#666" />
                    </TouchableOpacity>
                    <TouchableOpacity
                        style={styles.actionButton}
                        onPress={(e) => {
                            e.stopPropagation();
                            handleDeleteCard(card.id);
                        }}
                    >
                        <Ionicons name="trash-outline" size={20} color="#dc4446" />
                    </TouchableOpacity>
                </View>
            </View>
        </TouchableOpacity>
    );

    const renderAddModal = () => (
        <Modal
            visible={showAddModal}
            transparent={true}
            animationType="slide"
            onRequestClose={() => {
                resetForm();
                setShowAddModal(false);
            }}
        >
            <View style={styles.modalOverlay}>
                <View style={styles.modalContent}>
                    <View style={styles.modalHeader}>
                        <Text style={styles.modalTitle}>
                            {editingCard ? i18n.t('prepaidCards.editCard') : i18n.t('prepaidCards.addCard')}
                        </Text>
                        <TouchableOpacity
                            onPress={() => {
                                resetForm();
                                setShowAddModal(false);
                            }}
                            style={styles.modalCloseButton}
                        >
                            <Ionicons name="close" size={24} color="#666" />
                        </TouchableOpacity>
                    </View>

                    <View style={styles.modalBody}>
                        <Text style={styles.inputLabel}>{i18n.t('prepaidCards.cardName')}</Text>
                        <TextInput
                            style={styles.textInput}
                            placeholder={i18n.t('prepaidCards.cardNameRequired')}
                            value={cardName}
                            onChangeText={setCardName}
                        />

                        <Text style={styles.inputLabel}>{i18n.t('prepaidCards.cardType')}</Text>
                        <View style={styles.cardTypeSelector}>
                            <TouchableOpacity
                                style={[styles.cardTypeOption, cardType === 'amount' && styles.selectedCardType]}
                                onPress={() => setCardType('amount')}
                            >
                                <Text style={[styles.cardTypeText, cardType === 'amount' && styles.selectedCardTypeText]}>
                                    {i18n.t('prepaidCards.amountCard')}
                                </Text>
                            </TouchableOpacity>
                            <TouchableOpacity
                                style={[styles.cardTypeOption, cardType === 'count' && styles.selectedCardType]}
                                onPress={() => setCardType('count')}
                            >
                                <Text style={[styles.cardTypeText, cardType === 'count' && styles.selectedCardTypeText]}>
                                    {i18n.t('prepaidCards.countCard')}
                                </Text>
                            </TouchableOpacity>
                        </View>

                        {cardType === 'amount' ? (
                            <>
                                <Text style={styles.inputLabel}>{i18n.t('prepaidCards.cardBalance')}</Text>
                                <TextInput
                                    style={styles.textInput}
                                    placeholder={i18n.t('prepaidCards.invalidBalance')}
                                    value={cardBalance}
                                    onChangeText={setCardBalance}
                                    keyboardType="decimal-pad"
                                    returnKeyType="done"
                                />
                            </>
                        ) : (
                            <>
                                <Text style={styles.inputLabel}>{i18n.t('prepaidCards.cardCount')}</Text>
                                <TextInput
                                    style={styles.textInput}
                                    placeholder={i18n.t('prepaidCards.enterCardCount')}
                                    value={cardCountBalance}
                                    onChangeText={setCardCountBalance}
                                    keyboardType="number-pad"
                                    returnKeyType="done"
                                />
                            </>
                        )}

                        <Text style={styles.inputLabel}>{i18n.t('prepaidCards.rechargeDate')}</Text>
                        <TouchableOpacity
                            style={styles.datePickerButton}
                            onPress={() => setShowDatePicker(true)}
                        >
                            <Text style={styles.datePickerText}>
                                {rechargeDate.toISOString().split('T')[0]}
                            </Text>
                            <Ionicons name="calendar-outline" size={20} color="#666" />
                        </TouchableOpacity>

                        {showDatePicker && (
                            <DateTimePicker
                                value={rechargeDate}
                                mode="date"
                                display={Platform.OS === 'ios' ? 'inline' : 'default'}
                                onChange={(_, selectedDate) => {
                                    setShowDatePicker(false);
                                    if (selectedDate) {
                                        setRechargeDate(selectedDate);
                                    }
                                }}
                                maximumDate={new Date()}
                                textColor="#333"
                                accentColor="#E91E63"
                                themeVariant="light"
                                locale={i18n.locale === 'zh' ? 'zh-CN' : 'en-US'}
                            />
                        )}

                        <Text style={styles.inputLabel}>{i18n.t('prepaidCards.expiryDate')} {i18n.t('prepaidCards.optional')}</Text>
                        <TouchableOpacity
                            style={styles.datePickerButton}
                            onPress={() => setShowExpiryDatePicker(true)}
                        >
                            <Text style={styles.datePickerText}>
                                {expiryDate ? expiryDate.toISOString().split('T')[0] : i18n.t('prepaidCards.selectExpiryDate')}
                            </Text>
                            <Ionicons name="calendar-outline" size={20} color="#666" />
                        </TouchableOpacity>

                        {showExpiryDatePicker && (
                            <DateTimePicker
                                value={expiryDate || new Date()}
                                mode="date"
                                display={Platform.OS === 'ios' ? 'inline' : 'default'}
                                onChange={(_, selectedDate) => {
                                    setShowExpiryDatePicker(false);
                                    if (selectedDate) {
                                        setExpiryDate(selectedDate);
                                    }
                                }}
                                minimumDate={new Date()}
                                textColor="#333"
                                accentColor="#E91E63"
                                themeVariant="light"
                                locale={i18n.locale === 'zh' ? 'zh-CN' : 'en-US'}
                            />
                        )}

                        {expiryDate && (
                            <TouchableOpacity
                                style={styles.clearDateButton}
                                onPress={() => setExpiryDate(null)}
                            >
                                <Text style={styles.clearDateText}>{i18n.t('prepaidCards.clearExpiryDate')}</Text>
                            </TouchableOpacity>
                        )}
                    </View>

                    <View style={styles.modalActions}>
                        <TouchableOpacity
                            style={styles.cancelButton}
                            onPress={() => {
                                resetForm();
                                setShowAddModal(false);
                            }}
                        >
                            <Text style={styles.cancelButtonText}>{i18n.t('common.cancel')}</Text>
                        </TouchableOpacity>
                        <TouchableOpacity
                            style={styles.confirmButton}
                            onPress={handleAddCard}
                        >
                            <Text style={styles.confirmButtonText}>
                                {editingCard ? i18n.t('common.edit') : i18n.t('common.add')}
                            </Text>
                        </TouchableOpacity>
                    </View>
                </View>
            </View>
        </Modal>
    );

    const renderUsageModal = () => (
        <Modal
            visible={showUsageModal}
            transparent={true}
            animationType="slide"
            onRequestClose={() => {
                resetUsageForm();
                setShowUsageModal(false);
            }}
        >
            <View style={styles.modalOverlay}>
                <View style={styles.modalContent}>
                    <View style={styles.modalHeader}>
                        <Text style={styles.modalTitle}>
                            {editingUsage ? i18n.t('prepaidCards.editUsage') : i18n.t('prepaidCards.useCard')} - {selectedCard?.name}
                        </Text>
                        <TouchableOpacity
                            onPress={() => {
                                resetUsageForm();
                                setShowUsageModal(false);
                            }}
                            style={styles.modalCloseButton}
                        >
                            <Ionicons name="close" size={24} color="#666" />
                        </TouchableOpacity>
                    </View>

                    <View style={styles.modalBody}>
                        <Text style={styles.inputLabel}>{i18n.t('prepaidCards.usageAmount')}</Text>
                        <TextInput
                            style={styles.textInput}
                            placeholder={i18n.t('prepaidCards.enterUsageAmount')}
                            value={usageAmount}
                            onChangeText={setUsageAmount}
                            keyboardType="decimal-pad"
                            returnKeyType="done"
                            onSubmitEditing={() => {
                                // 当用户点击完成时，失去焦点
                                setUsageAmount(usageAmount);
                            }}
                        />

                        <Text style={styles.inputLabel}>{i18n.t('prepaidCards.usageNote')}</Text>
                        <TextInput
                            style={styles.textInput}
                            placeholder={i18n.t('prepaidCards.enterUsageNote')}
                            value={usageNote}
                            onChangeText={setUsageNote}
                        />

                        <Text style={styles.inputLabel}>{i18n.t('prepaidCards.usageDate')}</Text>
                        <TouchableOpacity
                            style={styles.datePickerButton}
                            onPress={() => setShowUsageDatePicker(true)}
                        >
                            <Text style={styles.datePickerText}>
                                {usageDate.toISOString().split('T')[0]}
                            </Text>
                            <Ionicons name="calendar-outline" size={20} color="#666" />
                        </TouchableOpacity>

                        {showUsageDatePicker && (
                            <DateTimePicker
                                value={usageDate}
                                mode="date"
                                display={Platform.OS === 'ios' ? 'inline' : 'default'}
                                onChange={(_, selectedDate) => {
                                    setShowUsageDatePicker(false);
                                    if (selectedDate) {
                                        setUsageDate(selectedDate);
                                    }
                                }}
                                maximumDate={new Date()}
                                textColor="#333"
                                accentColor="#E91E63"
                                themeVariant="light"
                                locale={i18n.locale === 'zh' ? 'zh-CN' : 'en-US'}
                            />
                        )}

                        {selectedCard && (
                            <Text style={styles.balanceInfo}>
                                {selectedCard.card_type === 'count'
                                    ? `${i18n.t('prepaidCards.remainingCount')}: ${selectedCard.count_balance}次`
                                    : `${i18n.t('prepaidCards.currentBalance')}: ${currency}${selectedCard.balance.toFixed(2)}`
                                }
                            </Text>
                        )}
                    </View>

                    <View style={styles.modalActions}>
                        <TouchableOpacity
                            style={styles.cancelButton}
                            onPress={() => {
                                resetUsageForm();
                                setShowUsageModal(false);
                            }}
                        >
                            <Text style={styles.cancelButtonText}>{i18n.t('common.cancel')}</Text>
                        </TouchableOpacity>
                        <TouchableOpacity
                            style={styles.confirmButton}
                            onPress={handleUseCard}
                        >
                            <Text style={styles.confirmButtonText}>{i18n.t('prepaidCards.useCard')}</Text>
                        </TouchableOpacity>
                    </View>
                </View>
            </View>
        </Modal>
    );

    const renderDetailModal = () => (
        <Modal
            visible={showDetailModal}
            transparent={true}
            animationType="slide"
            onRequestClose={() => setShowDetailModal(false)}
        >
            <View style={styles.modalOverlay}>
                <View style={[styles.modalContent, { maxHeight: '80%' }]}>
                    <View style={styles.modalHeader}>
                        <Text style={styles.modalTitle}>
                            {selectedCard?.name} - {i18n.t('prepaidCards.usageHistory')}
                        </Text>
                        <TouchableOpacity
                            onPress={() => setShowDetailModal(false)}
                            style={styles.modalCloseButton}
                        >
                            <Ionicons name="close" size={24} color="#666" />
                        </TouchableOpacity>
                    </View>

                    <ScrollView style={styles.modalBody}>
                        {usageRecords.length === 0 ? (
                            <View style={styles.emptyUsage}>
                                <Ionicons name="receipt-outline" size={48} color="#ccc" />
                                <Text style={styles.emptyUsageText}>{i18n.t('prepaidCards.noUsageRecords')}</Text>
                            </View>
                        ) : (
                            usageRecords.map((record) => (
                                <View key={record.id} style={styles.usageRecord}>
                                    <View style={styles.usageRecordHeader}>
                                        <View style={styles.usageRecordLeft}>
                                            <Text style={styles.usageAmount}>
                                                {selectedCard?.card_type === 'count'
                                                    ? `-${record.amount}次`
                                                    : `-${currency}${record.amount.toFixed(2)}`
                                                }
                                            </Text>
                                            <Text style={styles.usageDate}>{record.usage_date}</Text>
                                        </View>
                                        <View style={styles.usageRecordActions}>
                                            {/* <TouchableOpacity
                                                style={styles.usageActionButton}
                                                onPress={() => handleEditUsage(record)}
                                            >
                                                <Ionicons name="pencil" size={16} color="#666" />
                                            </TouchableOpacity> */}
                                            <TouchableOpacity
                                                style={[styles.usageActionButton, styles.deleteActionButton]}
                                                onPress={() => handleDeleteUsage(record.id)}
                                            >
                                                <Ionicons name="trash" size={16} color="#dc4446" />
                                            </TouchableOpacity>
                                        </View>
                                    </View>
                                    {record.note && (
                                        <Text style={styles.usageNote}>{record.note}</Text>
                                    )}
                                </View>
                            ))
                        )}
                    </ScrollView>
                </View>
            </View>
        </Modal>
    );

    return (
        <View style={styles.container}>
            <ScrollView
                style={styles.scrollView}
                contentContainerStyle={styles.scrollContent}
                showsVerticalScrollIndicator={false}
            >
                <TouchableOpacity
                    style={styles.addButton}
                    onPress={() => {
                        setShowAddModal(true);
                    }}
                >
                    <Ionicons name="add" size={24} color="white" />
                    <Text style={styles.addButtonText}>{i18n.t('prepaidCards.addCard')}</Text>
                </TouchableOpacity>

                {cards.length === 0 ? (
                    <View style={styles.emptyState}>
                        <Ionicons name="card-outline" size={64} color="#ccc" />
                        <Text style={styles.emptyText}>{i18n.t('prepaidCards.noCards')}</Text>
                        <Text style={styles.emptySubtext}>{i18n.t('prepaidCards.addFirstCard')}</Text>
                    </View>
                ) : (
                    <View style={styles.cardsList}>
                        {cards.map(renderCard)}
                    </View>
                )}
            </ScrollView>

            {renderAddModal()}
            {renderUsageModal()}
            {renderDetailModal()}
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#f5f5f5',
    },
    scrollView: {
        flex: 1,
        padding: 16,
    },
    scrollContent: {
        paddingBottom: 100, // 确保可以滚动到底部
    },
    addButton: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: '#E91E63',
        paddingVertical: 12,
        paddingHorizontal: 20,
        borderRadius: 12,
        marginBottom: 20,
        position: 'relative',
    },
    addButtonText: {
        color: 'white',
        fontSize: 16,
        fontWeight: '500',
        marginLeft: 8,
    },
    emptyState: {
        alignItems: 'center',
        justifyContent: 'center',
        paddingVertical: 60,
    },
    emptyText: {
        fontSize: 18,
        color: '#666',
        marginTop: 16,
        fontWeight: '500',
    },
    emptySubtext: {
        fontSize: 14,
        color: '#999',
        marginTop: 8,
        textAlign: 'center',
    },
    cardsList: {
        gap: 12,
    },
    cardItem: {
        backgroundColor: 'white',
        borderRadius: 12,
        padding: 16,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.1,
        shadowRadius: 3.84,
        elevation: 5,
    },
    cardHeader: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    cardIcon: {
        width: 48,
        height: 48,
        borderRadius: 24,
        backgroundColor: '#fce4ec',
        alignItems: 'center',
        justifyContent: 'center',
        marginRight: 12,
    },
    cardInfo: {
        flex: 1,
    },
    cardName: {
        fontSize: 16,
        fontWeight: '600',
        color: '#333',
        marginBottom: 4,
    },
    cardBalance: {
        fontSize: 14,
        color: '#E91E63',
        fontWeight: '500',
        marginBottom: 2,
    },
    cardDate: {
        fontSize: 12,
        color: '#666',
    },
    cardActions: {
        flexDirection: 'row',
        gap: 8,
    },
    actionButton: {
        padding: 8,
        borderRadius: 8,
        backgroundColor: '#f5f5f5',
    },
    // Modal styles
    modalOverlay: {
        flex: 1,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        justifyContent: 'center',
        alignItems: 'center',
    },
    modalContent: {
        backgroundColor: 'white',
        borderRadius: 16,
        width: '90%',
        maxWidth: 400,
    },
    modalHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        padding: 20,
        borderBottomWidth: 1,
        borderBottomColor: '#f0f0f0',
    },
    modalTitle: {
        fontSize: 18,
        fontWeight: '600',
        color: '#333',
    },
    modalCloseButton: {
        padding: 4,
    },
    modalBody: {
        padding: 20,
    },
    inputLabel: {
        fontSize: 14,
        fontWeight: '500',
        color: '#333',
        marginBottom: 8,
        marginTop: 12,
    },
    textInput: {
        borderWidth: 1,
        borderColor: '#ddd',
        borderRadius: 8,
        padding: 12,
        fontSize: 16,
        backgroundColor: '#f9f9f9',
    },
    datePickerButton: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        borderWidth: 1,
        borderColor: '#ddd',
        borderRadius: 8,
        padding: 12,
        backgroundColor: '#f9f9f9',
    },
    datePickerText: {
        fontSize: 16,
        color: '#333',
    },
    balanceInfo: {
        fontSize: 14,
        color: '#666',
        marginTop: 12,
        textAlign: 'center',
        fontStyle: 'italic',
    },
    emptyUsage: {
        alignItems: 'center',
        justifyContent: 'center',
        paddingVertical: 40,
    },
    emptyUsageText: {
        fontSize: 16,
        color: '#666',
        marginTop: 12,
    },
    usageRecord: {
        backgroundColor: '#f9f9f9',
        borderRadius: 8,
        padding: 12,
        marginBottom: 8,
    },
    usageRecordHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
    },
    usageRecordLeft: {
        flex: 1,
    },
    usageRecordActions: {
        flexDirection: 'row',
        gap: 8,
    },
    usageActionButton: {
        padding: 8,
        borderRadius: 6,
        backgroundColor: '#f0f0f0',
    },
    deleteActionButton: {
        backgroundColor: '#ffe6e6',
    },
    usageAmount: {
        fontSize: 16,
        fontWeight: '600',
        color: '#dc4446',
    },
    usageDate: {
        fontSize: 14,
        color: '#666',
    },
    usageNote: {
        fontSize: 14,
        color: '#333',
        marginTop: 4,
    },
    modalActions: {
        flexDirection: 'row',
        padding: 20,
        gap: 12,
    },
    cancelButton: {
        flex: 1,
        paddingVertical: 12,
        borderRadius: 8,
        backgroundColor: '#f5f5f5',
        alignItems: 'center',
    },
    cancelButtonText: {
        fontSize: 16,
        color: '#666',
    },
    confirmButton: {
        flex: 1,
        paddingVertical: 12,
        borderRadius: 8,
        backgroundColor: '#E91E63',
        alignItems: 'center',
    },
    confirmButtonText: {
        fontSize: 16,
        color: 'white',
        fontWeight: '500',
    },
    premiumBadge: {
        position: 'absolute',
        top: -8,
        right: -8,
        backgroundColor: '#FF9800',
        paddingHorizontal: 6,
        paddingVertical: 2,
        borderRadius: 8,
    },
    premiumBadgeText: {
        fontSize: 10,
        color: 'white',
        fontWeight: '600',
    },
    cardTypeSelector: {
        flexDirection: 'row',
        gap: 8,
        marginTop: 8,
    },
    cardTypeOption: {
        flex: 1,
        paddingVertical: 10,
        paddingHorizontal: 12,
        borderRadius: 8,
        borderWidth: 2,
        borderColor: '#ddd',
        alignItems: 'center',
        backgroundColor: '#f9f9f9',
    },
    selectedCardType: {
        borderColor: '#E91E63',
        backgroundColor: '#fce4ec',
    },
    cardTypeText: {
        fontSize: 14,
        fontWeight: '500',
        color: '#666',
    },
    selectedCardTypeText: {
        color: '#E91E63',
        fontWeight: '600',
    },
    clearDateButton: {
        marginTop: 8,
        paddingVertical: 8,
        paddingHorizontal: 12,
        backgroundColor: '#f0f0f0',
        borderRadius: 6,
        alignItems: 'center',
    },
    clearDateText: {
        fontSize: 14,
        color: '#666',
    },
});

export default PrepaidCards;
